import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Github, Linkedin, Mail, Heart } from 'lucide-react'

export function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="border-t bg-background">
      <div className="container py-12">
        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            {/* Brand */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
                  <span className="text-white font-bold text-sm">JB</span>
                </div>
                <span className="font-bold text-lg">Jundel Betinol</span>
              </div>
              <p className="text-muted-foreground text-sm">
                Graphic Artist & Software Developer creating secure, beautiful digital experiences.
              </p>
            </div>

            {/* Quick Links */}
            <div className="space-y-4">
              <h4 className="font-semibold">Quick Links</h4>
              <div className="space-y-2">
                <button className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                  About
                </button>
                <button className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                  Projects
                </button>
                <button className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                  Skills
                </button>
                <button className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                  Contact
                </button>
              </div>
            </div>

            {/* Connect */}
            <div className="space-y-4">
              <h4 className="font-semibold">Connect</h4>
              <div className="flex space-x-2">
                <Button variant="ghost" size="icon" asChild>
                  <a href="https://github.com" target="_blank" rel="noopener noreferrer">
                    <Github className="h-4 w-4" />
                  </a>
                </Button>
                <Button variant="ghost" size="icon" asChild>
                  <a href="https://www.linkedin.com/in/jundelbetinol" target="_blank" rel="noopener noreferrer">
                    <Linkedin className="h-4 w-4" />
                  </a>
                </Button>
                <Button variant="ghost" size="icon" asChild>
                  <a href="mailto:<EMAIL>">
                    <Mail className="h-4 w-4" />
                  </a>
                </Button>
              </div>
              <div className="space-y-2">
                <Badge variant="secondary" className="text-xs">
                  Available for Projects
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Remote Work
                </Badge>
              </div>
            </div>
          </div>

          <div className="border-t pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <p className="text-sm text-muted-foreground">
                © {currentYear} Jundel Betinol. All rights reserved.
              </p>
              <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                <span>Made with</span>
                <Heart className="h-4 w-4 text-red-500 fill-current" />
                <span>using React & shadcn/ui</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}