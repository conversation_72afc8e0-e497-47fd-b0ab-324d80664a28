import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Code, Palette, Cloud, Shield, Database, Smartphone } from 'lucide-react'

export function Skills() {
  const skillCategories = [
    {
      title: "Frontend Development",
      icon: Code,
      skills: [
        { name: "React", level: 90 },
        { name: "Angular", level: 85 },
        { name: "TypeScript", level: 88 },
        { name: "Tailwind CSS", level: 92 },
        { name: "HTML/CSS", level: 95 }
      ]
    },
    {
      title: "Backend Development",
      icon: Database,
      skills: [
        { name: "<PERSON><PERSON>", level: 85 },
        { name: "PhalconPHP", level: 80 },
        { name: "Node.js", level: 75 },
        { name: "MySQL", level: 88 },
        { name: "API Design", level: 90 }
      ]
    },
    {
      title: "Cloud & DevOps",
      icon: Cloud,
      skills: [
        { name: "AWS Lambda", level: 85 },
        { name: "API Gateway", level: 82 },
        { name: "RDS MySQL", level: 80 },
        { name: "S3 & CloudFront", level: 88 },
        { name: "VPC & Security", level: 75 }
      ]
    },
    {
      title: "Design & Creative",
      icon: Palette,
      skills: [
        { name: "UI/UX Design", level: 95 },
        { name: "Adobe Creative Suite", level: 98 },
        { name: "Figma", level: 90 },
        { name: "Brand Design", level: 95 },
        { name: "Print Design", level: 92 }
      ]
    },
    {
      title: "Security & Encryption",
      icon: Shield,
      skills: [
        { name: "End-to-End Encryption", level: 85 },
        { name: "Security Architecture", level: 80 },
        { name: "Authentication Systems", level: 88 },
        { name: "Data Protection", level: 85 },
        { name: "Secure Coding", level: 90 }
      ]
    },
    {
      title: "Mobile & Cross-Platform",
      icon: Smartphone,
      skills: [
        { name: "Responsive Design", level: 95 },
        { name: "Progressive Web Apps", level: 85 },
        { name: "Mobile-First Design", level: 90 },
        { name: "Cross-Browser Testing", level: 88 },
        { name: "Performance Optimization", level: 82 }
      ]
    }
  ]

  const tools = [
    "Visual Studio Code", "Git", "Docker", "Postman", "Figma", "Adobe Creative Suite",
    "AWS Console", "MySQL Workbench", "Chrome DevTools", "Slack", "Jira", "Notion"
  ]

  return (
    <section id="skills" className="py-20">
      <div className="container">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4">Skills & Expertise</Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Technical Proficiency
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              A comprehensive skill set spanning creative design, modern development, and cloud technologies
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {skillCategories.map((category, index) => (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                      <category.icon className="h-5 w-5 text-primary" />
                    </div>
                    <CardTitle className="text-lg">{category.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {category.skills.map((skill, i) => (
                      <div key={i} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">{skill.name}</span>
                          <span className="text-xs text-muted-foreground">{skill.level}%</span>
                        </div>
                        <Progress value={skill.level} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-none">
            <CardHeader>
              <CardTitle className="text-center">Tools & Technologies</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap justify-center gap-3">
                {tools.map((tool, index) => (
                  <Badge key={index} variant="secondary" className="hover:bg-primary hover:text-primary-foreground transition-colors cursor-default">
                    {tool}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}