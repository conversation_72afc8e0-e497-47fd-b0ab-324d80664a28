import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ExternalLink, Github, Shield, Users, Calendar, BarChart3, Smartphone, Factory } from 'lucide-react'

export function Projects() {
  const projects = [
    {
      title: "Team Announcements System",
      description: "Real-time communication platform with end-to-end encryption for secure team messaging and announcements.",
      icon: Users,
      features: ["Real-time notifications", "End-to-end encryption", "Role-based access", "Multi-platform support"],
      technologies: ["Angular", "Laravel", "MySQL", "AWS Lambda", "WebSocket"],
      status: "Production"
    },
    {
      title: "Events Management System",
      description: "Comprehensive event planning and management platform handling large-scale events with advanced analytics.",
      icon: Calendar,
      features: ["Event scheduling", "Attendee management", "Payment integration", "Analytics dashboard"],
      technologies: ["React", "PhalconPHP", "AWS RDS", "S3", "EventBridge"],
      status: "Production"
    },
    {
      title: "Fitness System",
      description: "Advanced fitness tracking application with personalized workout plans and progress monitoring.",
      icon: Smartphone,
      features: ["Workout tracking", "Progress analytics", "Social features", "Wearable integration"],
      technologies: ["React", "Laravel", "MySQL", "AWS API Gateway", "CloudWatch"],
      status: "Production"
    },
    {
      title: "Manufacturing Tracking System",
      description: "IoT-integrated system for real-time manufacturing process monitoring and quality control.",
      icon: Factory,
      features: ["Real-time monitoring", "Quality control", "Inventory management", "Predictive analytics"],
      technologies: ["Angular", "PhalconPHP", "AWS IoT", "RDS", "Lambda"],
      status: "Production"
    },
    {
      title: "App Center System",
      description: "Enterprise application management platform for centralized app distribution and monitoring.",
      icon: Smartphone,
      features: ["App distribution", "Version control", "Usage analytics", "Security monitoring"],
      technologies: ["React", "Laravel", "AWS S3", "CloudFront", "VPC"],
      status: "Production"
    },
    {
      title: "Portfolio Management Platform",
      description: "Advanced investment portfolio management system with real-time market data and analytics.",
      icon: BarChart3,
      features: ["Portfolio tracking", "Market analysis", "Risk assessment", "Automated reporting"],
      technologies: ["Angular", "PhalconPHP", "MySQL", "AWS SDK", "API Gateway"],
      status: "Production"
    }
  ]

  return (
    <section id="projects" className="py-20 bg-muted/30">
      <div className="container">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4">Projects</Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Featured Work
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Secure, scalable applications built with modern technologies and end-to-end encryption
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projects.map((project, index) => (
              <Card key={index} className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 bg-card/50 backdrop-blur">
                <CardHeader>
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                      <project.icon className="h-6 w-6 text-primary" />
                    </div>
                    <div className="flex items-center space-x-1">
                      <Shield className="h-4 w-4 text-green-500" />
                      <span className="text-xs text-green-500 font-medium">Secure</span>
                    </div>
                  </div>
                  <CardTitle className="group-hover:text-primary transition-colors">
                    {project.title}
                  </CardTitle>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    {project.description}
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-sm mb-2">Key Features:</h4>
                      <ul className="space-y-1">
                        {project.features.map((feature, i) => (
                          <li key={i} className="flex items-center text-sm text-muted-foreground">
                            <div className="w-1.5 h-1.5 rounded-full bg-primary mr-2" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold text-sm mb-2">Technologies:</h4>
                      <div className="flex flex-wrap gap-1">
                        {project.technologies.slice(0, 3).map((tech, i) => (
                          <Badge key={i} variant="secondary" className="text-xs">
                            {tech}
                          </Badge>
                        ))}
                        {project.technologies.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{project.technologies.length - 3}
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t">
                      <Badge variant="outline" className="text-xs">
                        {project.status}
                      </Badge>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                          <Github className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}